'''
Author       : winsonyang 
Date         : 2025-01-20 16:30:00
LastEditors  : winsonyang 
LastEditTime : 2025-01-20 16:30:00
FilePath     : /aigc-api-test/workflow/api/v1_workflow_chat_completions.py
Description  : 工作流聊天完成API接口

Copyright (c) 2025 by Ten<PERSON>, All Rights Reserved. 
'''
import json
import requests
from typing import Dict, Any, Optional, Generator, Tuple, List
from workflow.utils.api_utils import make_api_request
from workflow.utils.error_handling import WorkflowValidationError
from workflow.utils.log_utils import workflow_logger
from workflow.utils.sse_utils import process_sse_stream_with_headers


def v1_workflow_chat_completions(
    workflow_id: str,
    messages: List[Dict[str, str]],
    version: str = "1",
    stream: bool = True,
    parameters: Dict[str, Any] = {},
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Generator[Tuple[Dict[str, Any], Dict[str, str]], None, None]:
    """
    执行工作流聊天完成API
    
    Args:
        workflow_id: 工作流ID
        messages: 聊天消息数组，每个消息包含role和content字段
        version: API版本 (默认: "1")
        stream: 是否流式返回 (默认: True)
        parameters: 额外的执行参数 (默认: {})
        route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
        headers: 额外的请求头
        base_url: API基础URL (默认: https://test.hunyuan.woa.com)
        auth_token: 认证令牌
        
    Returns:
        生成器，返回(响应数据, 响应头)元组
        
    Raises:
        WorkflowValidationError: 如果必需参数未提供或格式不正确
        ApiRequestError: 如果API请求失败
    """
    # 参数验证
    if not workflow_id:
        raise WorkflowValidationError(
            message="workflow_id必须提供",
            details={"provided_parameters": {"workflow_id": workflow_id}}
        )
    
    if not messages or not isinstance(messages, list):
        raise WorkflowValidationError(
            message="messages必须提供且为非空数组",
            details={"provided_parameters": {"messages": messages}}
        )
    
    # 验证messages格式
    for i, message in enumerate(messages):
        if not isinstance(message, dict) or 'role' not in message or 'content' not in message:
            raise WorkflowValidationError(
                message=f"messages[{i}]格式不正确，必须包含role和content字段",
                details={"message_index": i, "message": message}
            )
    
    workflow_logger.workflow_operation("chat_completions", workflow_id, 
                                     parameters={"messages_count": len(messages), "version": version, "stream": stream})
    
    endpoint = "/openapi/v1/workflow/chat/completions/id"
    
    default_headers = {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream' if stream else 'application/json'
    }
    
    # 只有在route_env不为空时才添加X-Route-Env头
    if route_env:
        default_headers['X-Route-Env'] = route_env
    
    if auth_token:
        default_headers['Authorization'] = f'Bearer {auth_token}'
    
    if headers:
        default_headers.update(headers)
    
    url = f"{base_url}{endpoint}"
    
    payload = {
        "workflow_id": workflow_id,
        "messages": messages,
        "version": version,
        "stream": stream,
        "parameters": parameters
    }
    
    workflow_logger.api_request("POST", url, default_headers, payload)
    
    # Use stream=True to handle the streaming response
    with requests.post(
        url,
        headers=default_headers,
        data=json.dumps(payload),
        stream=stream
    ) as response:
        # Raise an exception for HTTP errors
        response.raise_for_status()
        
        # Get the headers once at the beginning
        response_headers = dict(response.headers)
        workflow_logger.api_response("POST", url, response.status_code, 0, {"headers": response_headers})
        
        if stream:
            # Use the common SSE processing utility
            for event_data, headers in process_sse_stream_with_headers(response, response_headers):
                yield event_data, headers
        else:
            # Handle non-streaming response
            try:
                response_data = response.json()
                yield response_data, response_headers
            except json.JSONDecodeError as e:
                workflow_logger.error("解析响应数据失败", {
                    "response_text": response.text,
                    "error": str(e)
                })
                raise 