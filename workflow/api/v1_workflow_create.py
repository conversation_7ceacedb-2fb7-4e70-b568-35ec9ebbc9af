'''
Author       : winsonyang 
Date         : 2025-03-11 16:21:13
LastEditors  : winsonyang 
LastEditTime : 2025-06-03 14:31:40
FilePath     : /aigc-api-test/workflow/api/v1_workflow_create.py
Description  : 

Copyright (c) 2025 by <PERSON><PERSON>, All Rights Reserved. 
'''
from typing import Dict, Any, Optional, Union
from workflow.utils.api_utils import make_api_request, load_workflow_from_file
from workflow.utils.error_handling import WorkflowValidationError, workflow_logger
from workflow.utils.response_utils import ApiResponse, process_workflow_id_response


def v1_workflow_create(
    workspace: str,
    name: str,
    description: str,
    workflow: Optional[Dict[str, Any]] = None,
    workflow_file: Optional[str] = None,
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a workflow using the Hunyuan API.
    
    Args:
        workspace: The workspace name
        name: The name of the workflow
        description: The description of the workflow
        workflow: The workflow configuration data (optional if workflow_file is provided)
        workflow_file: Path to a JSON file containing workflow configuration (optional)
        route_env: The route environment (optional, no X-Route-Env header if not set)
        headers: Additional headers to include in the request
        base_url: The base URL for the API (default: https://test.hunyuan.woa.com)
        auth_token: Authentication token for Bearer authorization
        
    Returns:
        The API response as a dictionary
        
    Raises:
        WorkflowValidationError: If neither workflow nor workflow_file is provided
        WorkflowConfigError: If workflow_file is provided but file doesn't exist or contains invalid JSON
        ApiRequestError: If the API request fails
    """
    if workflow is None and workflow_file is None:
        raise WorkflowValidationError(
            message="Either workflow or workflow_file must be provided",
            details={"provided_parameters": {"workflow": workflow is not None, "workflow_file": workflow_file is not None}}
        )
    
    workflow_logger.info(f"创建工作流: 名称={name}, 工作区={workspace}")
        
    if workflow_file is not None:
        workflow = load_workflow_from_file(workflow_file)

    endpoint = "/api/workflow/create"
        
    payload = {
        "workspace": workspace,
        "name": name,
        "description": description,
        "workflow_dsl": workflow
    }
    
    # 调试：打印payload
    workflow_logger.info("创建工作流payload", {
        "workspace": workspace,
        "name": name,
        "description": description,
        "workflow_dsl_keys": list(workflow.keys()) if isinstance(workflow, dict) else None
    })
    
    response = make_api_request(
        endpoint=endpoint,
        payload=payload,
        route_env=route_env,
        headers=headers,
        base_url=base_url,
        auth_token=auth_token
    )
    
    # 对响应进行基本验证，但不提取数据，以保持原有API的返回结构
    ApiResponse(response).validate_success("workflow_create")
    
    return response


def create_workflow_and_get_id(
    workspace: str,
    name: str,
    description: str,
    workflow: Optional[Dict[str, Any]] = None,
    workflow_file: Optional[str] = None,
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> str:
    """
    创建工作流并返回工作流ID
    
    此函数是v1_workflow_create的便捷包装，直接返回工作流ID而不是完整响应
    
    Args:
        与v1_workflow_create相同
        
    Returns:
        工作流ID
        
    Raises:
        与v1_workflow_create相同，以及：
        WorkflowValidationError: 如果响应中没有有效的工作流ID
    """
    response = v1_workflow_create(
        workspace=workspace,
        name=name,
        description=description,
        workflow=workflow,
        workflow_file=workflow_file,
        route_env=route_env,
        headers=headers,
        base_url=base_url,
        auth_token=auth_token
    )
    
    return process_workflow_id_response(response)