'''
Author       : winsonyang 
Date         : 2025-03-12 11:44:13
LastEditors  : winsonyang 
LastEditTime : 2025-04-15 13:30:00
FilePath     : /aigc-api-test/workflow/api/v1_workflow_execution_invoke.py
Description  : 

Copyright (c) 2025 by <PERSON><PERSON>, All Rights Reserved. 
'''
from typing import Dict, Any, Optional, Union
from workflow.utils.api_utils import make_api_request
from workflow.utils.error_handling import WorkflowValidationError, workflow_logger
from workflow.utils.response_utils import ApiResponse, process_execution_response


def v1_workflow_execution_invoke(
    workflow_id: str,
    is_async: bool = False,
    parameters: Dict[str, Any] = {},
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Dict[str, Any]:
    """
    Invoke a workflow execution using the Hunyuan API.
    
    Args:
        workflow_id: The ID of the workflow to invoke
        is_async: Whether to invoke the workflow asynchronously (default: False)
        parameters: Parameters for the workflow execution (default: {})
        route_env: The route environment (optional, no X-Route-Env header if not set)
        headers: Additional headers to include in the request
        base_url: The base URL for the API (default: https://test.hunyuan.woa.com)
        auth_token: Authentication token for Bearer authorization
        
    Returns:
        The API response as a dictionary
        
    Raises:
        WorkflowValidationError: If workflow_id is not provided
        ApiRequestError: If the API request fails
    """
    if not workflow_id:
        raise WorkflowValidationError(
            message="workflow_id must be provided",
            details={"provided_parameters": {"workflow_id": workflow_id}}
        )
    
    workflow_logger.info("执行工作流", 
                       workflow_id=workflow_id, 
                       is_async=is_async,
                       event="workflow.invoke",
                       parameters_count=len(parameters))
    
    endpoint = "/api/workflow/execution/invoke"
    
    payload = {
        "workflow_id": workflow_id,
        "is_async": is_async,
        "parameters": parameters
    }
    
    response = make_api_request(
        endpoint=endpoint,
        payload=payload,
        route_env=route_env,
        headers=headers,
        base_url=base_url,
        auth_token=auth_token
    )
    
    # 对响应进行基本验证，但不提取数据，以保持原有API的返回结构
    ApiResponse(response).validate_success("workflow_execution_invoke")
    
    return response


def invoke_workflow_and_get_result(
    workflow_id: str,
    is_async: bool = False,
    parameters: Dict[str, Any] = {},
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Dict[str, Any]:
    """
    执行工作流并返回执行结果
    
    此函数是v1_workflow_execution_invoke的便捷包装，直接返回执行结果数据而不是完整响应
    
    Args:
        与v1_workflow_execution_invoke相同
        
    Returns:
        工作流执行结果数据
        
    Raises:
        与v1_workflow_execution_invoke相同，以及：
        WorkflowValidationError: 如果响应中没有有效的执行结果
    """
    response = v1_workflow_execution_invoke(
        workflow_id=workflow_id,
        is_async=is_async,
        parameters=parameters,
        route_env=route_env,
        headers=headers,
        base_url=base_url,
        auth_token=auth_token
    )
    
    return process_execution_response(response)
