{"type": "CHATFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始", "description": "工作流起始节点"}, "next": ["IO_GET_NAME"], "input": null, "output": {"type": "object", "properties": {"chatHistory": {"type": "string", "description": "历史对话记录"}, "userPrompt": {"type": "string", "description": "用户输入"}}}}, {"node_id": "IO_GET_NAME", "node_type": "IO", "node_meta": {"name": "获取姓名", "description": "请求用户输入姓名"}, "next": ["IO_GET_AGE"], "input": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "您好！我需要收集一些基本信息。请告诉我您的姓名："}], "io": {"question": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "您好！我需要收集一些基本信息。请告诉我您的姓名：", "literal_type": "string"}], "stream": true}}, {"node_id": "IO_GET_AGE", "node_type": "IO", "node_meta": {"name": "获取年龄", "description": "请求用户输入年龄"}, "next": ["IO_GET_CITY"], "input": [{"id": "name", "name": "name", "input_type": "reference", "reference": {"node_id": "IO_GET_NAME", "name": "UserResponse", "type": "string"}}], "io": {"question": [{"id": "greeting", "name": "greeting", "input_type": "literal", "literal": "很高兴认识您，", "literal_type": "string"}, {"id": "name", "name": "name", "input_type": "reference", "reference": {"node_id": "IO_GET_NAME", "name": "UserResponse", "type": "string"}}, {"id": "age_prompt", "name": "age_prompt", "input_type": "literal", "literal": "！请问您的年龄是？", "literal_type": "string"}], "stream": true}}, {"node_id": "IO_GET_CITY", "node_type": "IO", "node_meta": {"name": "获取城市", "description": "请求用户输入所在城市"}, "next": ["CODE_PROCESS"], "input": [], "io": {"question": [{"id": "city_prompt", "name": "city_prompt", "input_type": "literal", "literal": "最后，请告诉我您所在的城市：", "literal_type": "string"}], "stream": true}}, {"node_id": "CODE_PROCESS", "node_type": "CODE", "node_meta": {"name": "处理用户信息", "description": "整理收集的用户信息"}, "next": ["LLM_SUMMARY"], "input": [{"id": "name", "name": "name", "input_type": "reference", "reference": {"node_id": "IO_GET_NAME", "name": "UserResponse", "type": "string"}}, {"id": "age", "name": "age", "input_type": "reference", "reference": {"node_id": "IO_GET_AGE", "name": "UserResponse", "type": "string"}}, {"id": "city", "name": "city", "input_type": "reference", "reference": {"node_id": "IO_GET_CITY", "name": "UserResponse", "type": "string"}}], "output": {"type": "object", "properties": {"user_info": {"type": "string"}}}, "code": {"code": "async def main(args):\n    name = args['name']\n    age = args['age']\n    city = args['city']\n    \n    user_info = f\"姓名：{name}\\n年龄：{age}\\n城市：{city}\"\n    \n    return {\n        'user_info': user_info\n    }"}}, {"node_id": "LLM_SUMMARY", "node_type": "LLM", "node_meta": {"name": "生成总结", "description": "根据收集的信息生成总结"}, "next": ["END"], "input": [{"id": "user_info", "name": "user_info", "input_type": "reference", "reference": {"node_id": "CODE_PROCESS", "name": "user_info", "type": "string"}}], "output": {"type": "object", "properties": {"content": {"type": "string"}}}, "llm": {"provider": "Hunyuan", "model": "hunyuan-turbos-latest", "stream": true, "temperature": 0.7, "max_tokens": 2048, "system_prompt": "你是一个友好的助手，正在帮助用户完成信息收集。", "user_prompt": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "用户信息收集完成，以下是收集到的信息：\n\n{{user_info}}\n\n请生成一个友好的总结和感谢信息，确认已经收集到所有必要的信息。"}]}, "message": {"streaming_output": true, "message_type": "STRING"}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束", "description": "输出总结信息"}, "next": null, "input": [{"name": "summary", "input_type": "reference", "reference": {"node_id": "LLM_SUMMARY", "name": "content", "type": "string"}}], "message": {"streaming_output": true, "message_type": "STRING", "string_format": [{"name": "summary", "input_type": "reference", "reference": {"node_id": "LLM_SUMMARY", "name": "content", "type": "string"}}]}}]}