{"graph_id": "w_bd57e56f-f8cd-11ef-b078-3e4f84a9709a", "type": "CHATFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["node1", "node2"], "input": null}, {"node_id": "node1", "node_type": "LLM", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer 0qHtuxlmXiO-BcATjSg2-BBbAofnSyf5"}, {"id": "user_prompt_2", "name": "prompt", "input_type": "reference", "reference": {"node_id": "START", "name": "prompt", "type": "string"}, "literal": "你谁啊？"}], "llm": {"polaris_name": "polaris://trpc.amai.hunyuan-openapi-for-yuanqi", "polaris_namespace": "Test", "api_token": "0qHtuxlmXiO-BcATjSg2-BBbAofnSyf5", "provider": "Hunyuan", "model": "hunyuan-standard", "stream": true, "chat_history": {"id": "chat_history_count", "name": "chat_history_count", "input_type": "literal", "literal": "15"}, "system_prompt": [{"id": "system_prompt", "name": "system_prompt", "input_type": "literal", "literal": "你是一个AI助手，请回答用户的问题。"}], "user_prompt": [{"id": "user_prompt_1", "name": "user_prompt_1", "input_type": "literal", "literal": "用户的问题是："}, {"id": "user_prompt_2", "name": "prompt", "input_type": "reference", "reference": {"node_id": "START", "name": "prompt", "type": "string"}}]}}, {"node_id": "node2", "node_type": "FUNC", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "sleep_time", "name": "sleep_time", "input_type": "reference", "reference": {"node_id": "START", "name": "sleep_time", "type": "int"}}, {"id": "test_output", "name": "test_output", "input_type": "literal", "literal": "test_output"}], "func": {"func_name": "sleep"}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": null, "input": [{"id": "xxx", "name": "content", "input_type": "reference", "reference": {"node_id": "node1", "name": "content", "type": "string"}}, {"id": "yyy", "name": "output_val", "input_type": "reference", "reference": {"node_id": "node2", "name": "test_output", "type": "string"}}], "message": {"streaming_output": false, "message_type": "STRING", "string_format": null}}]}