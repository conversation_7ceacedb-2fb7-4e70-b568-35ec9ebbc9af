{"type": "CHATFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始", "description": "工作流起始节点"}, "next": ["IO_GET_CHOICE"], "input": null, "output": {"type": "object", "properties": {"chatHistory": {"type": "string", "description": "历史对话记录"}, "userPrompt": {"type": "string", "description": "用户输入"}}}}, {"node_id": "IO_GET_CHOICE", "node_type": "IO", "node_meta": {"name": "获取用户选择", "description": "让用户选择服务类型"}, "next": ["BRANCH_CHECK"], "input": [{"id": "menu", "name": "menu", "input_type": "literal", "literal": "请选择您需要的服务：\nA. 技术支持\nB. 产品咨询\nC. 投诉建议\n\n请输入选项（A/B/C）："}], "io": {"question": [{"id": "menu", "name": "menu", "input_type": "literal", "literal": "请选择您需要的服务：\nA. 技术支持\nB. 产品咨询\nC. 投诉建议\n\n请输入选项（A/B/C）：", "literal_type": "string"}], "stream": true}}, {"node_id": "BRANCH_CHECK", "node_type": "BRANCH", "node_meta": {"name": "检查选择", "description": "根据用户选择进行分支"}, "next": null, "input": [{"id": "choice", "name": "choice", "input_type": "reference", "reference": {"node_id": "IO_GET_CHOICE", "name": "UserResponse", "type": "string"}}], "branches": [{"branch_id": "tech_branch", "condition": "choice == \"A\" or choice == \"a\"", "next": ["IO_TECH_SUPPORT"]}, {"branch_id": "product_branch", "condition": "choice == \"B\" or choice == \"b\"", "next": ["IO_PRODUCT_CONSULT"]}, {"branch_id": "complaint_branch", "condition": "choice == \"C\" or choice == \"c\"", "next": ["IO_COMPLAINT"]}, {"branch_id": "default_branch", "condition": "true", "next": ["LLM_INVALID"]}]}, {"node_id": "IO_TECH_SUPPORT", "node_type": "IO", "node_meta": {"name": "技术支持详情", "description": "获取技术问题详情"}, "next": ["LLM_TECH_RESPONSE"], "input": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "您选择了技术支持。请详细描述您遇到的技术问题："}], "io": {"question": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "您选择了技术支持。请详细描述您遇到的技术问题：", "literal_type": "string"}], "stream": true}}, {"node_id": "IO_PRODUCT_CONSULT", "node_type": "IO", "node_meta": {"name": "产品咨询详情", "description": "获取产品咨询详情"}, "next": ["LLM_PRODUCT_RESPONSE"], "input": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "您选择了产品咨询。请告诉我您想了解的产品信息："}], "io": {"question": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "您选择了产品咨询。请告诉我您想了解的产品信息：", "literal_type": "string"}], "stream": true}}, {"node_id": "IO_COMPLAINT", "node_type": "IO", "node_meta": {"name": "投诉建议详情", "description": "获取投诉建议详情"}, "next": ["LLM_COMPLAINT_RESPONSE"], "input": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "您选择了投诉建议。请描述您的意见或建议："}], "io": {"question": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "您选择了投诉建议。请描述您的意见或建议：", "literal_type": "string"}], "stream": true}}, {"node_id": "LLM_INVALID", "node_type": "LLM", "node_meta": {"name": "无效选择", "description": "处理无效选择"}, "next": ["END"], "input": [{"id": "choice", "name": "choice", "input_type": "reference", "reference": {"node_id": "IO_GET_CHOICE", "name": "UserResponse", "type": "string"}}], "output": {"type": "object", "properties": {"content": {"type": "string"}}}, "llm": {"url": "http://hunyuanapi.woa.com/openapi/v1/chat/completions", "api_token": "YWzsgXYTGSV9EGzTMtxL1NyAarBBWGbi", "provider": "Hunyuan", "model": "hunyuan-turbos-latest", "stream": true, "temperature": 0.7, "moderation": true, "max_tokens": 1024, "chat_history": {"id": "chat_history", "name": "chat_history", "input_type": "reference", "reference": {"node_id": "START", "name": "chatHistory", "type": "string"}}, "user_prompt": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "用户输入了无效的选项：{{choice}}。请友好地提示用户选择有效的选项（A、B或C）。"}]}, "message": {"streaming_output": true, "message_type": "STRING"}}, {"node_id": "LLM_TECH_RESPONSE", "node_type": "LLM", "node_meta": {"name": "技术支持回复", "description": "生成技术支持回复"}, "next": ["END"], "input": [{"id": "issue", "name": "issue", "input_type": "reference", "reference": {"node_id": "IO_TECH_SUPPORT", "name": "UserResponse", "type": "string"}}], "output": {"type": "object", "properties": {"content": {"type": "string"}}}, "llm": {"url": "http://hunyuanapi.woa.com/openapi/v1/chat/completions", "api_token": "YWzsgXYTGSV9EGzTMtxL1NyAarBBWGbi", "provider": "Hunyuan", "model": "hunyuan-turbos-latest", "stream": true, "temperature": 0.7, "moderation": true, "max_tokens": 2048, "chat_history": {"id": "chat_history", "name": "chat_history", "input_type": "reference", "reference": {"node_id": "START", "name": "chatHistory", "type": "string"}}, "user_prompt": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "用户遇到的技术问题：{{issue}}\n\n请提供专业的技术支持回复，包括可能的解决方案和建议。"}]}, "message": {"streaming_output": true, "message_type": "STRING"}}, {"node_id": "LLM_PRODUCT_RESPONSE", "node_type": "LLM", "node_meta": {"name": "产品咨询回复", "description": "生成产品咨询回复"}, "next": ["END"], "input": [{"id": "query", "name": "query", "input_type": "reference", "reference": {"node_id": "IO_PRODUCT_CONSULT", "name": "UserResponse", "type": "string"}}], "output": {"type": "object", "properties": {"content": {"type": "string"}}}, "llm": {"url": "http://hunyuanapi.woa.com/openapi/v1/chat/completions", "api_token": "YWzsgXYTGSV9EGzTMtxL1NyAarBBWGbi", "provider": "Hunyuan", "model": "hunyuan-turbos-latest", "stream": true, "temperature": 0.7, "moderation": true, "max_tokens": 2048, "chat_history": {"id": "chat_history", "name": "chat_history", "input_type": "reference", "reference": {"node_id": "START", "name": "chatHistory", "type": "string"}}, "user_prompt": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "用户的产品咨询：{{query}}\n\n请提供详细的产品信息回复。"}]}, "message": {"streaming_output": true, "message_type": "STRING"}}, {"node_id": "LLM_COMPLAINT_RESPONSE", "node_type": "LLM", "node_meta": {"name": "投诉建议回复", "description": "生成投诉建议回复"}, "next": ["END"], "input": [{"id": "feedback", "name": "feedback", "input_type": "reference", "reference": {"node_id": "IO_COMPLAINT", "name": "UserResponse", "type": "string"}}], "output": {"type": "object", "properties": {"content": {"type": "string"}}}, "llm": {"url": "http://hunyuanapi.woa.com/openapi/v1/chat/completions", "api_token": "YWzsgXYTGSV9EGzTMtxL1NyAarBBWGbi", "provider": "Hunyuan", "model": "hunyuan-turbos-latest", "stream": true, "temperature": 0.7, "moderation": true, "max_tokens": 2048, "chat_history": {"id": "chat_history", "name": "chat_history", "input_type": "reference", "reference": {"node_id": "START", "name": "chatHistory", "type": "string"}}, "user_prompt": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "用户的投诉建议：{{feedback}}\n\n请提供诚恳的回复，感谢用户的反馈，并说明我们会如何处理这个问题。"}]}, "message": {"streaming_output": true, "message_type": "STRING"}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束", "description": "输出最终回复"}, "next": null, "input": [{"name": "response", "input_type": "reference", "reference": {"node_id": "PREVIOUS", "name": "content", "type": "string"}}], "message": {"streaming_output": true, "message_type": "STRING", "string_format": [{"name": "response", "input_type": "reference", "reference": {"node_id": "PREVIOUS", "name": "content", "type": "string"}}]}}]}