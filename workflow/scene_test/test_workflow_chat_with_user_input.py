'''
Author       : winsonyang 
Date         : 2025-01-20 18:00:00
LastEditors  : winsonyang 
LastEditTime : 2025-07-08 18:14:18
FilePath     : /aigc-api-test/workflow/scene_test/test_workflow_chat_with_user_input.py
Description  : 测试工作流聊天完成与用户输入交互场景

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
import json
import time
import pytest
from typing import Dict, Any, List

from workflow.api.v1_workflow_create import v1_workflow_create
from workflow.api.v1_workflow_publish import v1_workflow_publish
from workflow.api.v1_workflow_chat_completions import v1_workflow_chat_completions
from workflow.api.v1_workflow_submit_user_outputs import v1_workflow_submit_user_outputs
from workflow.utils.test_utils import load_test_cases, get_test_case_ids
from workflow.utils.structured_logger import workflow_logger


# 加载测试用例并提取ID
test_cases = load_test_cases("workflow/scene_test/test_cases_IO.json")


def extract_execute_id_from_stream(event_data: Dict[str, Any]) -> str:
    """从流式响应中提取execute_id"""
    if "execute_id" in event_data:
        return event_data["execute_id"]
    
    # 尝试从choices中提取
    if "choices" in event_data:
        for choice in event_data["choices"]:
            if "execute_id" in choice:
                return choice["execute_id"]
            if "delta" in choice and "execute_id" in choice["delta"]:
                return choice["delta"]["execute_id"]
    
    return None


def is_waiting_for_user_input(event_data: Dict[str, Any]) -> bool:
    """判断是否在等待用户输入"""
    workflow_logger.debug(f"Checking event for user_replies: {event_data}")
    if "choices" in event_data:
        for choice in event_data["choices"]:
            # 检查finish_reason是否为user_replies
            if "finish_reason" in choice:
                workflow_logger.debug(f"Found finish_reason: {choice['finish_reason']}")
                if choice["finish_reason"] == "user_replies":
                    return True
            # 检查delta中的finish_reason
            if "delta" in choice and "finish_reason" in choice["delta"]:
                workflow_logger.debug(f"Found delta finish_reason: {choice['delta']['finish_reason']}")
                if choice["delta"]["finish_reason"] == "user_replies":
                    return True
    return False


def run_workflow_with_user_inputs(
    workflow_id: str,
    messages: List[Dict[str, str]],
    user_inputs_sequence: List[Dict[str, Any]],
    route_env: str,
    base_url: str,
    auth_token: str,
    max_iterations: int = 10
) -> Dict[str, Any]:
    """
    运行工作流并处理用户输入
    
    Args:
        workflow_id: 工作流ID
        messages: 初始消息
        user_repliess_sequence: 用户输入序列
        route_env: 路由环境
        base_url: 基础URL
        auth_token: 认证令牌
        max_iterations: 最大迭代次数
        
    Returns:
        最终的响应结果
    """
    execute_id = None
    current_input_index = 0
    iteration_count = 0
    final_response = None
    
    # 首次调用chat completions
    workflow_logger.info("开始执行工作流", {
        "workflow_id": workflow_id,
        "initial_messages": messages
    })
    
    for event_data, headers in v1_workflow_chat_completions(
        workflow_id=workflow_id,
        messages=messages,
        stream=True,
        route_env=route_env,
        base_url=base_url,
        auth_token=auth_token
    ):
        # 调试：打印事件数据
        workflow_logger.info("收到事件数据", {
            "event_data": event_data,
            "has_choices": "choices" in event_data,
            "finish_reason": event_data.get("choices", [{}])[0].get("finish_reason") if event_data.get("choices") else None
        })
        
        # 提取execute_id
        if not execute_id:
            execute_id = extract_execute_id_from_stream(event_data)
            if execute_id:
                workflow_logger.info("获取到execute_id", {"execute_id": execute_id})
        
        # 检查是否需要用户输入
        if is_waiting_for_user_input(event_data):
            workflow_logger.info("工作流等待用户输入", {
                "execute_id": execute_id,
                "current_input_index": current_input_index
            })
            break
        
        # 检查是否完成
        if "choices" in event_data:
            for choice in event_data["choices"]:
                if "finish_reason" in choice and choice["finish_reason"] == "stop":
                    workflow_logger.info("工作流执行完成")
                    return event_data
        
        final_response = event_data
    
    # 处理用户输入循环
    while current_input_index < len(user_inputs_sequence) and iteration_count < max_iterations:
        iteration_count += 1
        
        if not execute_id:
            raise ValueError("未能获取execute_id")
        
        # 获取当前用户输入
        current_user_outputs = user_inputs_sequence[current_input_index]
        workflow_logger.info("提交用户输入", {
            "execute_id": execute_id,
            "input_index": current_input_index,
            "user_outputs": current_user_outputs
        })
        
        # 提交用户输入
        wait_for_input = False
        for event_data, headers in v1_workflow_submit_user_outputs(
            workflow_id=workflow_id,
            execute_id=execute_id,
            user_outputs=current_user_outputs,
            stream=True,
            route_env=route_env,
            base_url=base_url,
            auth_token=auth_token
        ):
            # 检查是否需要更多用户输入
            if is_waiting_for_user_input(event_data):
                wait_for_input = True
                current_input_index += 1
                workflow_logger.info("工作流继续等待用户输入", {
                    "next_input_index": current_input_index
                })
                break
            
            # 检查是否完成
            if "choices" in event_data:
                for choice in event_data["choices"]:
                    if "finish_reason" in choice and choice["finish_reason"] == "stop":
                        workflow_logger.info("工作流执行完成")
                        return event_data
            
            final_response = event_data
        
        # 如果不再等待输入，说明工作流已完成
        if not wait_for_input:
            break
    
    if iteration_count >= max_iterations:
        workflow_logger.warning("达到最大迭代次数", {
            "max_iterations": max_iterations,
            "current_input_index": current_input_index
        })
    
    return final_response


@pytest.mark.parametrize("test_case", test_cases, ids=get_test_case_ids(test_cases))
def test_workflow_chat_with_user_input(test_case, workflow_route_env, workflow_base_url, workflow_auth_token):
    """
    测试工作流聊天完成与用户输入交互场景
    
    测试步骤：
    1. 读取测试用例设置的工作流DSL，创建工作流
    2. 调用发布接口，发布工作流，记录工作流version
    3. 调用/openapi/v1/workflow/chat/completions/id接口，运行工作流
    4. 调用/openapi/v1/workflow/submit_user_outputs接口，继续工作流，直到返回结果：choices.finish_reason: "stop"
    """
    test_params = test_case["test_params"]
    workflow_file = test_params.get("workflow_file")
    workflow = test_params.get("workflow")
    
    # 确保 test_params 包含 workflow 或 workflow_file
    if workflow_file is None and workflow is None:
        pytest.fail("Either workflow or workflow_file must be provided in test case")
    
    try:
        # 步骤1: 创建工作流
        workflow_logger.info("创建工作流", {"test_name": test_case["test_name"]})
        create_response = v1_workflow_create(
            workspace=test_params["workspace"],
            description=test_params["description"],
            name=test_params["name"],
            workflow=workflow,
            workflow_file=workflow_file,
            route_env=workflow_route_env,
            base_url=workflow_base_url,
            auth_token=workflow_auth_token
        )
        
        create_body = create_response["body"]
        assert create_body["code"] == 0, f"创建工作流失败: {create_body}"
        workflow_id = create_body["data"]["workflow_id"]
        workflow_logger.info("工作流创建成功", {"workflow_id": workflow_id})
        
        # 步骤2: 发布工作流
        workflow_logger.info("发布工作流", {"workflow_id": workflow_id})
        publish_response = v1_workflow_publish(
            workflow_id=workflow_id,
            route_env=workflow_route_env,
            base_url=workflow_base_url,
            auth_token=workflow_auth_token
        )
        
        publish_body = publish_response["body"]
        assert publish_body["code"] == 0, f"发布工作流失败: {publish_body}"
        
        # 获取版本信息（如果响应中包含）
        version = publish_body.get("data", {}).get("version", "1")
        workflow_logger.info("工作流发布成功", {
            "workflow_id": workflow_id,
            "version": version
        })
        
        # 等待一下确保发布生效
        time.sleep(1)
        
        # 步骤3-4: 运行工作流并处理用户输入
        messages = test_params.get("messages", [{"role": "user", "content": "开始工作流"}])
        user_inputs_sequence = test_params.get("user_inputs_sequence", [])
        
        final_response = run_workflow_with_user_inputs(
            workflow_id=workflow_id,
            messages=messages,
            user_inputs_sequence=user_inputs_sequence,
            route_env=workflow_route_env,
            base_url=workflow_base_url,
            auth_token=workflow_auth_token
        )
        
        # 验证最终结果
        assert final_response is not None, "未收到最终响应"
        
        # 验证finish_reason为stop
        if "choices" in final_response:
            finish_reasons = []
            for choice in final_response["choices"]:
                if "finish_reason" in choice:
                    finish_reasons.append(choice["finish_reason"])
            
            assert "stop" in finish_reasons, f"工作流未正常结束，finish_reasons: {finish_reasons}"
        
        # 如果测试用例中有预期输出，进行验证
        if "expected_output" in test_case:
            # 这里可以添加更详细的输出验证逻辑
            pass
        
        workflow_logger.info("测试通过", {
            "test_name": test_case["test_name"],
            "workflow_id": workflow_id
        })
        
    except Exception as e:
        workflow_logger.error("测试失败", {
            "test_name": test_case["test_name"],
            "error": str(e)
        })
        raise