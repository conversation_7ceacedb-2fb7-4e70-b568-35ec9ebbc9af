{"type": "CHATFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始", "description": "工作流起始节点"}, "next": ["IO_001"], "input": null, "output": {"type": "object", "properties": {"chatHistory": {"type": "string", "description": "历史对话记录，最多30轮"}, "userPrompt": {"type": "string", "description": "用户当前轮次的输入问题"}}}}, {"node_id": "IO_001", "node_type": "IO", "node_meta": {"name": "获取用户输入", "description": "请求用户输入一个数字"}, "next": ["CODE_001"], "input": [], "output": {"type": "object", "properties": {"UserResponse": {"type": "string", "description": "用户输入的响应"}}}, "io": {"question": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "请输入一个数字，我将计算它的平方：", "literal_type": "string"}], "stream": true}}, {"node_id": "CODE_001", "node_type": "EVAL", "node_meta": {"name": "计算平方", "description": "计算输入数字的平方"}, "next": ["LLM_001"], "input": [{"id": "number", "name": "number", "input_type": "reference", "reference": {"node_id": "IO_001", "name": "UserResponse", "type": "string"}}], "output": {"type": "object", "properties": {"result": {"type": "integer"}, "original": {"type": "string"}}}, "code": {"code": "async def main(args):\n    import math\n    number_str = args['number']\n    try:\n        number = float(number_str)\n        result = int(number * number)\n        return {\n            'result': result,\n            'original': number_str\n        }\n    except ValueError:\n        return {\n            'result': 0,\n            'original': number_str\n        }"}}, {"node_id": "LLM_001", "node_type": "LLM", "node_meta": {"name": "生成回复", "description": "生成最终回复"}, "next": ["END"], "input": [{"id": "original", "name": "original", "input_type": "reference", "reference": {"node_id": "CODE_001", "name": "original", "type": "string"}}, {"id": "result", "name": "result", "input_type": "reference", "reference": {"node_id": "CODE_001", "name": "result", "type": "integer"}}], "output": {"type": "object", "properties": {"content": {"type": "string"}}}, "llm": {"provider": "Hunyuan", "model": "hunyuan-turbos-latest", "stream": true, "temperature": 0.7, "max_tokens": 2048, "system_prompt": "你是一个友好的数学助手。", "user_prompt": [{"id": "prompt_template", "name": "prompt_template", "input_type": "literal", "literal": "用户输入的数字是 {{original}}，它的平方是 {{result}}。请生成一个友好的回复，说明计算结果。"}]}, "message": {"streaming_output": true, "message_type": "STRING"}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束", "description": "输出最终结果"}, "next": null, "input": [{"name": "response", "input_type": "reference", "reference": {"node_id": "LLM_001", "name": "content", "type": "string"}}], "message": {"streaming_output": true, "message_type": "STRING", "string_format": [{"name": "response", "input_type": "reference", "reference": {"node_id": "LLM_001", "name": "content", "type": "string"}}]}}]}